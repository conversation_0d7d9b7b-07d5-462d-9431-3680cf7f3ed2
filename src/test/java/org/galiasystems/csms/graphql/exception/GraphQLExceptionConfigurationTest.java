package org.galiasystems.csms.graphql.exception;

import org.junit.jupiter.api.Test;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test to verify that the GraphQL exception handler is properly configured
 * and registered as a service.
 */
class GraphQLExceptionConfigurationTest {

    @Test
    void testServiceLoaderConfiguration() throws IOException {
        // Verify that the service loader file exists and contains our exception handler
        InputStream serviceFile = getClass().getClassLoader()
            .getResourceAsStream("META-INF/services/io.smallrye.graphql.api.ErrorExtensionProvider");
        
        assertNotNull(serviceFile, "Service loader file should exist");
        
        String content = new BufferedReader(new InputStreamReader(serviceFile, StandardCharsets.UTF_8))
            .lines()
            .collect(Collectors.joining("\n"));
        
        assertTrue(content.contains("org.galiasystems.csms.graphql.exception.GraphQLExceptionHandler"),
            "Service loader file should contain our exception handler class");
    }

    @Test
    void testExceptionHandlerInstantiation() {
        // Verify that our exception handler can be instantiated
        assertDoesNotThrow(() -> {
            GraphQLExceptionHandler handler = new GraphQLExceptionHandler();
            assertNotNull(handler);
            assertEquals("exception-details", handler.getKey());
        });
    }

    @Test
    void testApplicationPropertiesConfiguration() throws IOException {
        // Verify that the application.properties contains the GraphQL configuration
        InputStream propertiesFile = getClass().getClassLoader()
            .getResourceAsStream("application.properties");
        
        assertNotNull(propertiesFile, "application.properties should exist");
        
        String content = new BufferedReader(new InputStreamReader(propertiesFile, StandardCharsets.UTF_8))
            .lines()
            .collect(Collectors.joining("\n"));
        
        assertTrue(content.contains("quarkus.smallrye-graphql.show-runtime-exception-message"),
            "application.properties should contain GraphQL exception configuration");
        
        assertTrue(content.contains("java.lang.RuntimeException"),
            "Configuration should include RuntimeException");
        
        assertTrue(content.contains("java.lang.IllegalStateException"),
            "Configuration should include IllegalStateException");
    }
}
