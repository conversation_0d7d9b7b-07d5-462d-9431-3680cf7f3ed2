package org.galiasystems.csms.graphql.exception;

import io.smallrye.graphql.api.ErrorExtensionProvider;
import jakarta.json.JsonObject;
import jakarta.json.JsonValue;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test for GraphQL exception handler to verify proper exception message handling.
 */
class GraphQLExceptionHandlerTest {

    @Test
    void testRuntimeExceptionHandling() {
        GraphQLExceptionHandler handler = new GraphQLExceptionHandler();
        
        // Test RuntimeException
        RuntimeException runtimeException = new RuntimeException("Adapter for '-1' Charging Station not found!");
        JsonValue result = handler.mapValueFrom(runtimeException);
        
        assertNotNull(result);
        assertTrue(result instanceof JsonObject);
        
        JsonObject jsonObject = (JsonObject) result;
        assertEquals("RuntimeException", jsonObject.getString("classification"));
        assertEquals("Adapter for '-1' Charging Station not found!", jsonObject.getString("message"));
    }

    @Test
    void testIllegalStateExceptionHandling() {
        GraphQLExceptionHandler handler = new GraphQLExceptionHandler();
        
        // Test IllegalStateException
        IllegalStateException illegalStateException = new IllegalStateException("Unknown adapter type");
        JsonValue result = handler.mapValueFrom(illegalStateException);
        
        assertNotNull(result);
        assertTrue(result instanceof JsonObject);
        
        JsonObject jsonObject = (JsonObject) result;
        assertEquals("IllegalStateException", jsonObject.getString("classification"));
        assertEquals("Unknown adapter type", jsonObject.getString("message"));
    }

    @Test
    void testOtherExceptionHandling() {
        GraphQLExceptionHandler handler = new GraphQLExceptionHandler();
        
        // Test other exception types
        Exception otherException = new Exception("Some other error");
        JsonValue result = handler.mapValueFrom(otherException);
        
        assertNotNull(result);
        assertTrue(result instanceof JsonObject);
        
        JsonObject jsonObject = (JsonObject) result;
        assertEquals("SystemError", jsonObject.getString("classification"));
        assertEquals("An unexpected error occurred", jsonObject.getString("message"));
    }

    @Test
    void testExceptionWithNullMessage() {
        GraphQLExceptionHandler handler = new GraphQLExceptionHandler();
        
        // Test RuntimeException with null message
        RuntimeException runtimeException = new RuntimeException((String) null);
        JsonValue result = handler.mapValueFrom(runtimeException);
        
        assertNotNull(result);
        assertTrue(result instanceof JsonObject);
        
        JsonObject jsonObject = (JsonObject) result;
        assertEquals("RuntimeException", jsonObject.getString("classification"));
        assertEquals("Unknown error", jsonObject.getString("message"));
    }

    @Test
    void testErrorExtensionProviderInterface() {
        GraphQLExceptionHandler handler = new GraphQLExceptionHandler();
        
        // Verify it implements the correct interface
        assertTrue(handler instanceof ErrorExtensionProvider);
        assertEquals("exception-details", handler.getKey());
    }
}
