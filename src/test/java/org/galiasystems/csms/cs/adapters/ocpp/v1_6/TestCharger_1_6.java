package org.galiasystems.csms.cs.adapters.ocpp.v1_6;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.node.ArrayNode;
import jakarta.enterprise.inject.Vetoed;
import jakarta.websocket.ClientEndpoint;
import org.galiasystems.csms.cs.adapters.ocpp.OCPPVersion;
import org.galiasystems.csms.cs.adapters.ocpp.TestCharger;
import org.galiasystems.csms.cs.adapters.ocpp.messages.MessageType;
import org.galiasystems.csms.cs.adapters.ocpp.v1_6.model.messages.Action;
import org.galiasystems.csms.cs.adapters.ocpp.v1_6.model.payloads.bootNotification.BootNotificationRequest;
import org.galiasystems.csms.cs.adapters.ocpp.v1_6.model.payloads.changeAvailability.AvailabilityStatus;
import org.galiasystems.csms.cs.adapters.ocpp.v1_6.model.payloads.changeAvailability.AvailabilityType;
import org.galiasystems.csms.cs.adapters.ocpp.v1_6.model.payloads.changeAvailability.ChangeAvailabilityRequest;
import org.galiasystems.csms.cs.adapters.ocpp.v1_6.model.payloads.changeAvailability.ChangeAvailabilityResponse;
import org.galiasystems.csms.cs.adapters.ocpp.v1_6.model.payloads.heartbeat.HeartbeatRequest;

import java.util.UUID;
import java.util.concurrent.ExecutionException;

@ClientEndpoint
@Vetoed
public class TestCharger_1_6 extends TestCharger {

    private AvailabilityType availability;

    public TestCharger_1_6(final long chargingStationId, final String wsUrl, final String wsPassword) {
        super(chargingStationId, wsUrl, wsPassword);
    }

    @Override
    public void connect() {
        super.connect(OCPPVersion.OCPP_1_6);
    }

    @Override
    public ArrayNode createBootNotificationRequest() {
        var bootNotificationRequest = new BootNotificationRequest("serial01", "model-0", "serial01", "charger-v1", "1.0.0", "iccid01", "imsi01", "meterSerial01", "meterType01");
        var uuid = UUID.randomUUID().toString();
        return createMessage(MessageType.CallMessage, uuid, Action.BootNotification.name(), bootNotificationRequest);
    }

    @Override
    public void handleChangeAvailability(ArrayNode arrayNode) {
        logger.info("ChangeAvailabilityRequest received: " + arrayNode);
        ChangeAvailabilityRequest request;
        try {
            request = objectMapper.treeToValue(arrayNode.get(3), ChangeAvailabilityRequest.class);
            this.availability = request.type();
            ChangeAvailabilityResponse response = new ChangeAvailabilityResponse(AvailabilityStatus.Accepted);
            sendResponse(MessageType.CallResultMessage, arrayNode.get(1).asText(), response);
        } catch (JsonProcessingException | ExecutionException | InterruptedException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public ArrayNode createHeartbeat() {
        return createMessage(MessageType.CallMessage, UUID.randomUUID().toString(), Action.Heartbeat.name(), new HeartbeatRequest());
    }
}