package org.galiasystems.csms.cs.adapters.ocpp;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import jakarta.websocket.*;
import org.galiasystems.csms.cs.adapters.ocpp.messages.CallMessage;
import org.galiasystems.csms.cs.adapters.ocpp.messages.MessageType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.messages.Action;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.bootNotification.BootNotificationResponse;

import java.net.URI;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.logging.Logger;

import static org.junit.jupiter.api.Assertions.assertEquals;

public abstract class TestCharger extends Endpoint {

    protected final Logger logger = Logger.getLogger(TestCharger.class.getName());
    protected final LinkedBlockingQueue<String> unprocessedMessages = new LinkedBlockingQueue<>();
    protected final Map<String, ArrayNode> messagesById = new HashMap<>();
    protected Session session;

    protected String serialNumber;
    protected String model;
    protected String vendorName;
    protected String firmwareVersion;
    protected long chargingStationId;
    protected Integer heartbeatInterval;
    protected ObjectMapper objectMapper;
    protected String wsUrl;
    protected String wsPassword;

    public TestCharger(final long chargingStationId, final String wsUrl, final String wsPassword) {
        this(chargingStationId, "serial01", "model-0", "charger-v1", "1.0.0", wsUrl, wsPassword);
    }

    public TestCharger(final long chargingStationId, final String serialNumber, final String model,
                       final String vendorName, final String firmwareVersion, final String wsUrl, final String wsPassword) {
        this.chargingStationId = chargingStationId;
        this.serialNumber = serialNumber;
        this.model = model;
        this.vendorName = vendorName;
        this.firmwareVersion = firmwareVersion;
        this.wsUrl = wsUrl;
        this.wsPassword = wsPassword;

        this.objectMapper = new ObjectMapper();
        this.objectMapper.registerModule(new JavaTimeModule());
    }

    public abstract ArrayNode createBootNotificationRequest();

    public abstract ArrayNode createHeartbeat();

    public abstract void connect();

    public abstract void handleChangeAvailability(ArrayNode arrayNode);

    public void connect(final OCPPVersion ocppVersion) {
        WebSocketContainer container = ContainerProvider.getWebSocketContainer();
        try {
            ClientEndpointConfig config = ClientEndpointConfig.Builder.create()
                    .configurator(new TestChargerConfigurator(ocppVersion, String.valueOf(this.chargingStationId), this.wsPassword))
                    .build();
            this.session = container.connectToServer(this, config, new URI(this.wsUrl));

        } catch (Exception e) {
            logger.severe("Cannot connect to server: " + e.getClass().getName() + ": " + e.getMessage());
            throw new RuntimeException(e);
        }

        logger.info(chargingStationId + " is connected.");
    }

    public long getChargingStationId() {
    	return this.chargingStationId;
    }

    public ArrayNode sendRequest(ArrayNode request) throws InterruptedException, JsonProcessingException {
    	final RemoteEndpoint.Async asyncRemote = this.session.getAsyncRemote();

        asyncRemote.sendText(request.toString());

        messagesById.put(request.get(1).asText(), request);
        logger.info("Message sent by test charger: " + request);

        final var response = this.unprocessedMessages.poll(5, TimeUnit.SECONDS);

        return (ArrayNode) this.objectMapper.readTree(response);
    }

    public <T, A extends Enum<A>> CallMessage<T, A> getNextRequest(final Class<T> clazz, final Function<String, A> getAction) throws InterruptedException, JsonProcessingException {

        final var request = this.unprocessedMessages.poll(5, TimeUnit.SECONDS);

        final ArrayNode requestArray = (ArrayNode) this.objectMapper.readTree(request);
        		
        assertEquals(MessageType.CallMessage.getMessageTypeId(), requestArray.get(0).asInt());
        final T payload = this.objectMapper.readValue(requestArray.get(3).toString(), clazz);
        return new CallMessage<T, A>(requestArray.get(1).asText(), getAction.apply(requestArray.get(2).asText()), payload);
    }

    public void sendResponse(MessageType messageType, String uuid, Object payload) throws InterruptedException, ExecutionException {
        final var response = createMessage(messageType, uuid, null, payload);
        final RemoteEndpoint.Async remoteEndpoint = this.session.getAsyncRemote();
        final Future<Void> sendOperation = remoteEndpoint.sendText(response.toString());
        sendOperation.get();
        logger.info("Response sent by test client: " + response);
    }

    @Override
    public void onOpen(Session session, EndpointConfig config) {
        // Add the message handler here
        session.addMessageHandler(new MessageHandler.Whole<String>() {
            @Override
            public void onMessage(String message) {
                logger.info("Message received by test charger: " + message);
                unprocessedMessages.add(message);

                ArrayNode arrayNode;
                try {
                    arrayNode = (ArrayNode) objectMapper.readTree(message);
                } catch (JsonProcessingException e) {
                    throw new RuntimeException(e);
                }
                String messageNumber = arrayNode.get(1).asText();
                var originalMessage = messagesById.get(messageNumber);

                if (originalMessage != null && originalMessage.get(2).asText().equals(Action.BootNotification.name())) {
                    BootNotificationResponse response;
                    try {
                        response = objectMapper.treeToValue(arrayNode.get(2), BootNotificationResponse.class);
                    } catch (JsonProcessingException e) {
                        throw new RuntimeException(e);
                    }
                    heartbeatInterval = response.interval();
                } else if (
                        List.of(org.galiasystems.csms.cs.adapters.ocpp.v1_6.model.messages.Action.ChangeAvailability.name(), Action.ChangeAvailability.name()).contains(arrayNode.get(2).asText())
                ) {
                    handleChangeAvailability(arrayNode);
                }

            }
        });
    }

    public void onClose(Session session, CloseReason closeReason) {
        logger.info("Connection closed: " + closeReason);
    }

    public void onError(Session session, Throwable error) {
        logger.severe("WebSocket error: " + error.getClass().getName() + ": " + error.getMessage() + " " + Arrays.toString(error.getStackTrace()));
    }


    public ArrayNode createMessage(MessageType messageType, String uuid, String actionName, Object payload) {
    	
        this.objectMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        ArrayNode req = this.objectMapper.createArrayNode();

        req.add(messageType.getMessageTypeId());
        req.add(uuid);
        if (actionName != null) {
            req.add(actionName);
        }
        ObjectNode payloadJson = this.objectMapper.valueToTree(payload);

        req.add(payloadJson);

        return req;
    }

    public static class TestChargerConfigurator extends ClientEndpointConfig.Configurator {
        private final OCPPVersion ocppVersion;
        private final String currentUserName;
        private final String currentPassword;

        public TestChargerConfigurator() {
            this.ocppVersion = OCPPVersion.OCPP_1_6;
            this.currentUserName = "test";
            this.currentPassword = "test";
        }

        public TestChargerConfigurator(OCPPVersion ocppVersion, String userName, String password) {
            this.ocppVersion = ocppVersion;
            this.currentUserName = userName;
            this.currentPassword = password;
        }

		@Override
		public void beforeRequest(final Map<String, List<String>> headers) {
			super.beforeRequest(headers);
            final String encodedCredentials = Base64.getEncoder().encodeToString((currentUserName + ":" + currentPassword).getBytes());
			
			final List<String> values = new ArrayList<>();
	        values.add("Basic " + encodedCredentials);
	        headers.put("Authorization", values);
            headers.put("Sec-WebSocket-Protocol", List.of(ocppVersion.getVersion()));
		}
    }
}