package org.galiasystems.csms.cs.adapters.ocpp.v2_0_1;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.node.ArrayNode;
import jakarta.enterprise.inject.Vetoed;
import jakarta.websocket.ClientEndpoint;
import org.galiasystems.csms.cs.adapters.ocpp.OCPPVersion;
import org.galiasystems.csms.cs.adapters.ocpp.TestCharger;
import org.galiasystems.csms.cs.adapters.ocpp.messages.MessageType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.messages.Action;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.bootNotification.BootNotificationRequest;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.bootNotification.BootReasonEnumType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.bootNotification.ChargingStationType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.bootNotification.ModemType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.changeAvailability.ChangeAvailabilityRequest;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.changeAvailability.ChangeAvailabilityResponse;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.changeAvailability.ChangeAvailabilityStatusEnumType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.changeAvailability.OperationalStatusEnumType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.common.AttributeEnumType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.common.ComponentType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.common.EVSEType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.common.VariableType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.heartbeat.HeartbeatRequest;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.notifyReport.*;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.ExecutionException;

@ClientEndpoint
@Vetoed
public class TestCharger_2_0_1 extends TestCharger {
    protected final ChargingStationType stationType;
    protected OperationalStatusEnumType availability;

    public TestCharger_2_0_1(final long chargingStationId, final String wsUrl, final String wsPassword) {
        super(chargingStationId, wsUrl, wsPassword);
        this.stationType = new ChargingStationType("serial01", "model-0", "charger-v1", "1.0.0", new ModemType("iccid01", "imsi01"));
    }

    @Override
    public void connect() {
        super.connect(OCPPVersion.OCPP_2_0_1);
    }

    @Override
    public ArrayNode createBootNotificationRequest() {
        var bootNotificationRequest = new BootNotificationRequest(BootReasonEnumType.PowerUp, this.stationType);
        var uuid = UUID.randomUUID().toString();
        return createMessage(MessageType.CallMessage, uuid, Action.BootNotification.name(), bootNotificationRequest);
    }

    @Override
    public void handleChangeAvailability(ArrayNode arrayNode) {
        ChangeAvailabilityRequest request;
        try {
            request = objectMapper.treeToValue(arrayNode.get(2), ChangeAvailabilityRequest.class);
            this.availability = request.operationalStatus();
            ChangeAvailabilityResponse response = new ChangeAvailabilityResponse(ChangeAvailabilityStatusEnumType.Accepted, null);
            sendResponse(MessageType.CallResultMessage, arrayNode.get(1).asText(), response);
        } catch (JsonProcessingException | ExecutionException | InterruptedException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public ArrayNode createHeartbeat() {
        return createMessage(MessageType.CallMessage, UUID.randomUUID().toString(), Action.Heartbeat.name(), new HeartbeatRequest());
    }

    public ArrayNode createNotifyReportRequest() {
        final int requestId = 1;

        final List<ReportDataType> reportData = new ArrayList<>();
        final EVSEType eVSEType = new EVSEType(1, 1);
        final ComponentType component = new ComponentType("componentName", "componentInstance", eVSEType);

        final VariableType variable = new VariableType("variableName", "variableInstance");

        final List<VariableAttributeType> variableAttribute = new ArrayList<>();
        final VariableAttributeType variableAttributeTypeActual = new VariableAttributeType(AttributeEnumType.Actual,
                "variableValueActual", MutabilityEnumType.ReadWrite);
        final VariableAttributeType variableAttributeTypeMin = new VariableAttributeType(AttributeEnumType.MinSet,
                "variableValueMin", MutabilityEnumType.ReadOnly);
        final VariableAttributeType variableAttributeTypeMax = new VariableAttributeType(AttributeEnumType.MaxSet,
                "variableValueMax", MutabilityEnumType.ReadOnly);

        variableAttribute.add(variableAttributeTypeActual);
        variableAttribute.add(variableAttributeTypeMin);
        variableAttribute.add(variableAttributeTypeMax);

        final VariableCharacteristicsType variableCharacteristics = new VariableCharacteristicsType("unit",
                DataEnumType.string, new BigDecimal(10), new BigDecimal(20), "valuesList", true);


        final ReportDataType reportDataType = new ReportDataType(component, variable, variableAttribute, variableCharacteristics);
        reportData.add(reportDataType);

        return createNotifyReportRequest(requestId, reportData);
    }

    public ArrayNode createNotifyReportRequest(final int requestId, final List<ReportDataType> reportData) {


        final NotifyReportRequest notifyReportRequest = new NotifyReportRequest(requestId, ZonedDateTime.now(), false, 0, reportData);

        final var uuid = UUID.randomUUID().toString();
        return createMessage(MessageType.CallMessage, uuid, Action.NotifyReport.name(), notifyReportRequest);
    }
}