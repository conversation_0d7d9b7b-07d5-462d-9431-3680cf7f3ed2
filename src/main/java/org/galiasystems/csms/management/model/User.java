package org.galiasystems.csms.management.model;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import org.galiasystems.csms.management.model.enums.Role;

import jakarta.validation.constraints.NotBlank;

public class User {
	
	private final Long id;
	private final Long tenantId;
	private @NotBlank String userName;
	private String password;
	private final Map<Role, UserRole> roles = new HashMap<>();
	
	public User(final Long id, final Long tenantId, final String userName, final String password) {
		super();
		this.id = id;
		this.tenantId = tenantId;
		this.userName = userName;
		this.password = password;
	}

	public Long getId() {
		return id;
	}

	public Long getTenantId() {
		return tenantId;
	}

	public String getUserName() {
		return userName;
	}
	
	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getPassword() {
		return password;
	}
	
	public void setPassword(String password) {
		this.password = password;
	}

	public Map<Role, UserRole> getRoles() {
		return roles;
	}
	
	public void addRoleChargingStation(final Long chargingStationId) {
		addToRoles(Role.ChargingStation, (userRole) -> userRole.addChargingStationId(chargingStationId));
	}
	
	public void addRoleGlobalAdmin() {
		addRole(Role.GlobalAdmin);
	}

	public void addRoleTenantAdmin() {
		addRole(Role.TenantAdmin);
	}
	
	public void addRoleLocationAdmin(final long locationId) {
		addToRoles(Role.LocationAdmin, (userRole) -> userRole.addLocationId(locationId));
	}
	
	public void addRolePowerGridAdmin(final long powerGridId) {
		addToRoles(Role.PowerGridAdmin, (userRole) -> userRole.addPowerGridId(powerGridId));
	}

	public void addRoleChargingStationAdmin(final Long chargingStationId) {
		addToRoles(Role.ChargingStationAdmin, (userRole) -> userRole.addChargingStationId(chargingStationId));
	}
	
	public void addRoleRegisteredUser() {
		addRole(Role.RegisteredUser);
	}
	
	private void addRole(final Role role) {
		addToRoles(role, (userRole) -> {});
	}
	
	private void addToRoles(final Role role, final Consumer<UserRole> consumer) {
		roles.compute(role, (key, value) -> {
			final UserRole userRole;
			if (value == null) {
				userRole = new UserRole(role);
			} else {
				userRole = value;
			}
			consumer.accept(userRole);
			return userRole;
		});
	}
	
	public Set<String> getRoleNames() {
		return getRoles().keySet().stream()
				.map(Enum::name)
				.collect(Collectors.toSet());
	}
	
	public boolean isGlobalAdmin() {
		final Set<String> roleNames = getRoleNames();
		return roleNames.contains(Role.GlobalAdmin.name());
	}
	
	public boolean isTenantAdmin() {
		final Set<String> roleNames = getRoleNames();
		return roleNames.contains(Role.TenantAdmin.name());
	}
	
	public void validate() {
		if (isGlobalAdmin()) {
			final Set<String> roleNames = getRoleNames();
			if (roleNames.size() != 1) {
				throw new IllegalStateException("Global admins can not have other roles!"
						+ "userName: " + userName + " "
						+ "roles: " + roleNames);
			}
			
			if (tenantId != null) {
				throw new IllegalStateException("Global admins can not have tenant!"
						+ "userName: " + userName + " "
						+ "tenantId: " + tenantId);
			}
			
		} else {
			if (tenantId == null) {
				final Set<String> roleNames = getRoleNames();
				throw new IllegalStateException("Not Global admins must have tenant!"
						+ "userName: " + userName + " "
						+ "roles: " + roleNames);
			}
		}
		
		
	}

	@Override
	public String toString() {
		return "User[" +
				"id=" + id +
				", tenantId=" + tenantId +
				", userName='" + userName + '\'' +
				", password='" + password + '\'' +
				", roles=" + roles +
				']';
	}
}