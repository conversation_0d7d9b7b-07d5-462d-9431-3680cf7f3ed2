package org.galiasystems.csms.management.csms;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.time.ZonedDateTime;
import java.util.Collection;

import org.eclipse.microprofile.config.ConfigProvider;
import org.galiasystems.csms.cs.ChargingStationAdapterProxy;
import org.galiasystems.csms.management.csms.base.CsmsServiceBase;
import org.galiasystems.csms.management.filter.ChargingStationFilter;
import org.galiasystems.csms.management.model.ChargingStation;
import org.galiasystems.csms.management.model.IdToken;
import org.galiasystems.csms.management.model.enums.AvailabilityStatus;
import org.galiasystems.csms.management.model.enums.ChargingStationStatus;
import org.galiasystems.csms.management.model.enums.ResetType;
import org.galiasystems.csms.management.repository.ChargingStationRepository;
import org.galiasystems.csms.management.repository.TransactionalService;
import org.galiasystems.csms.management.types.*;
import org.galiasystems.csms.management.types.enums.MessageTrigger;
import org.galiasystems.csms.security.SecurityUtils;
import org.jboss.logging.Logger;

import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;

@ApplicationScoped
public class ChargingStationServiceImpl extends CsmsServiceBase implements ChargingStationService {

	@Inject
    private Logger log;

	@Inject
    private TransactionalService transactionalService;

	@Inject
    private ChargingStationRepository chargingStationRepository;

	@Inject
	private UserService userService;

	@Inject
    private ChargingStationAdapterProxy chargingStationAdapter;

	@Override
	public Uni<ChargingStation> registerChargingStation(final String name) {
		return this.transactionalService.doInTransaction(
				() -> this.chargingStationRepository.createChargingStation(
								new ChargingStation.Builder()
										.name(name)
										.status(ChargingStationStatus.New)
										.availabilityStatus(AvailabilityStatus.Unknown)
										.build(),
							chargingStation -> {
								chargingStation.setUrl(createUrlForChargingStation(chargingStation));
							}
						)
						.onItem()
						.invoke((newChargingStation) -> {
								if (this.log.isDebugEnabled()) {
									this.log.debug("Charging Station registered: " + newChargingStation.toString());
								}
						})
						.call((chargingStation) -> {
							final String password = SecurityUtils.generateStrongPassword(15);
							chargingStation.setPassword(password);
							return this.userService.createChargingStationUser(chargingStation.getId(), password);
						})
						.call(chargingStation -> {
							var currentUser = this.getCurrentUser();
									currentUser.addRoleChargingStationAdmin(chargingStation.getId());
									return this.userService.updateUser(currentUser);
								}
						)
				);
	}

	@Override
	public Uni<ChargingStation> getChargingStation(final long id) {
    	return this.chargingStationRepository.getChargingStation(id);
    }

	@Override
	public Uni<Collection<ChargingStation>> getChargingStations(final ChargingStationFilter filter) {
		return this.chargingStationRepository.getChargingStations(filter);
    }

	@Override
	public Uni<ChangeAvailabilityResult> changeAvailability(final long chargingStationId, final Integer evseId, final Integer connectorId,
			final AvailabilityStatus availabilityStatus) {
		return this.chargingStationAdapter.changeAvailability(chargingStationId, evseId, connectorId, availabilityStatus);
	}

	@Override
	public Uni<ResetResult> reset(final long id, final ResetType resetType,
			final Integer evseId) {
		return this.chargingStationAdapter.reset(id, resetType, evseId);
	}

	@Override
	public Uni<ChargingStation> updateChargingStation(long chargingStationId, String name) {
		return this.chargingStationRepository.updateChargingStation(chargingStationId, chargingStation -> {
			chargingStation.setName(name);
		});
	}

	@Override
	public Uni<RequestStartTransactionResult> requestStartTransaction(final long chargingStationId, final Integer evseId, final long remoteStartId, 
    		final IdToken idToken) {
		return this.chargingStationAdapter.requestStartTransaction(chargingStationId, evseId, remoteStartId, idToken);
	}

	@Override
	public Uni<RequestStopTransactionResult> requestStopTransaction(final long chargingStationId, final String transactionId) {
		return this.chargingStationAdapter.requestStopTransaction(chargingStationId, transactionId);
	}

	@Override
	public Uni<GetDiagnosticsResult> getDiagnostics(final long chargingStationId, final String location,
													final Integer retries, final Integer retryInterval,
													final ZonedDateTime startTime, final ZonedDateTime stopTime) {
		return this.chargingStationAdapter.getDiagnostics(chargingStationId, location, retries,
				retryInterval, startTime, stopTime);
	}

	@Override
	public Uni<TriggerMessageResult> triggerMessage(final long chargingStationId, final MessageTrigger messageTrigger, final Integer connectorId) {
		return this.chargingStationAdapter.triggerMessage(chargingStationId, messageTrigger, connectorId);
	}

	private String createUrlForChargingStation(final ChargingStation chargingStation) {
		String port = ConfigProvider.getConfig().getValue("quarkus.http.port", String.class);
		InetAddress inetAddress;
		try {
			inetAddress = InetAddress.getLocalHost();
		} catch (UnknownHostException e) {
			throw new RuntimeException(e);
		}
		String ipAddress = inetAddress.getHostAddress();
		return "ws://" + ipAddress + ":" + port + "/websocket/ocpp/" + chargingStation.getId();
	}
}