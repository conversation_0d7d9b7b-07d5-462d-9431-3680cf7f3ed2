package org.galiasystems.csms.graphql.exception;

import io.smallrye.graphql.api.ErrorExtensionProvider;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.json.Json;
import jakarta.json.JsonValue;
import org.jboss.logging.Logger;

/**
 * GraphQL exception handler that provides proper error messages for service layer exceptions.
 * This handler ensures that RuntimeException messages are properly
 * propagated to GraphQL responses instead of showing generic "System error" messages.
 */
@ApplicationScoped
public class GraphQLExceptionHandler implements ErrorExtensionProvider {

    private static final Logger log = Logger.getLogger(GraphQLExceptionHandler.class);

    @Override
    public String getKey() {
        return "exception-details";
    }

    @Override
    public JsonValue mapValueFrom(Throwable exception) {
        log.debug("Handling GraphQL exception: " + exception.getClass().getName() + " - " + exception.getMessage());

        // Handle RuntimeExceptions by preserving their messages
        if (exception instanceof RuntimeException) {
            return Json.createObjectBuilder()
                    .add("classification", exception.getClass().getSimpleName())
                    .add("message", exception.getMessage() != null ? exception.getMessage() : "Unknown error")
                    .build();
        }

        // For other exceptions, provide a generic error message
        return Json.createObjectBuilder()
                .add("classification", "SystemError")
                .add("message", "An unexpected error occurred")
                .build();
    }
}