package org.galiasystems.csms.graphql;

import io.smallrye.mutiny.Uni;
import jakarta.inject.Inject;
import org.eclipse.microprofile.graphql.*;
import org.galiasystems.csms.graphql.model.*;
import org.galiasystems.csms.graphql.utils.GraphqlEntityMapper;
import org.galiasystems.csms.management.Csms;
import org.galiasystems.csms.management.csms.ChargingStationService;
import org.galiasystems.csms.management.csms.ReportService;
import org.galiasystems.csms.management.model.IdToken;
import org.galiasystems.csms.management.model.enums.AvailabilityStatus;
import org.galiasystems.csms.management.model.enums.IdTokenType;
import org.galiasystems.csms.management.model.enums.ResetType;
import org.galiasystems.csms.management.types.enums.MessageTrigger;
import org.galiasystems.csms.utils.VertxUtil;

import java.time.ZonedDateTime;
import java.util.Collection;
import java.util.List;

@GraphQLApi
public class ChargingStationResolver {

	@Inject
	private Csms managerService;

	@Inject
	private ChargingStationService chargingStationService;

	@Inject
	private ReportService reportService;

	@Inject
	private GraphqlEntityMapper entityMapper;

	@Inject
	private EvseResolver evseResolver;

	@Query("chargingStation")
	@Description("Get charging station by id")
	public Uni<ChargingStationGraphql> getChargingStation(final @Id @Name("id") @NonNull long id) {

        return this.chargingStationService
        		.getChargingStation(id)
        		.onItem()
        		.transform(this.entityMapper::createChargingStationGraphql);
    }

	@Query("chargingStations")
	@Description("Get charging stations")
	public @NonNull Uni<Collection<@NonNull ChargingStationGraphql>> getChargingStations(
			final @Name("filter") ChargingStationFilterGraphql filter) {

		return this.chargingStationService
				.getChargingStations(this.entityMapper.createChargingStationFilter(filter))
				.onItem()
				.transform((chargingStations) -> chargingStations.stream()
						.map(this.entityMapper::createChargingStationGraphql).toList());
	}

	@Mutation("registerChargingStation")
	@Description("Register a charging station")
	public @NonNull Uni<ChargingStationGraphql> registerChargingStation(final @Name("name") String name) {

		return this.chargingStationService.registerChargingStation(name).onItem()
				.transform(this.entityMapper::createChargingStationGraphql);

	}

	@Mutation("updateChargingStation")
	@Description("Update a charging station")
	public @NonNull Uni<ChargingStationGraphql> updateChargingStation(final @Id @Name("chargingStationId") @NonNull long chargingStationId,
																	  final @Name("name") String name) {

		return this.chargingStationService.updateChargingStation(chargingStationId, name).onItem()
				.transform(this.entityMapper::createChargingStationGraphql);

	}

	@Mutation("changeAvailability")
	@Description("Change the availability of a charging station or an EVSE or a Connector")
	public Uni<ChangeAvailabilityResultGraphql> changeAvailability(final @Id @Name("chargingStationId") @NonNull long chargingStationId,
																   final @Name("evseId") Integer evseId,
																   final @Name("connectorId") Integer connectorId,
																   final @Name("availabilityStatus") @NonNull AvailabilityStatus availabilityStatus) {

		return this.chargingStationService.changeAvailability(chargingStationId, evseId, connectorId, availabilityStatus)
				.onItem()
				.transform(this.entityMapper::createChangeAvailabilityResultGraphql);
	}

	@Mutation("reset")
	@Description("Reset a charging station or EVSE")
	public Uni<ResetResultGraphql> reset(final @Id @Name("id") @NonNull long id,
										 final @Name("evseId") Integer evseId,
										 final @Name("resetType") @NonNull ResetType resetType) {

		return this.chargingStationService.reset(id, resetType, evseId)
				.onItem()
				.transform(this.entityMapper::createResetResultGraphql);
	}

	@Mutation("requestStartTransaction")
	@Description("Start a transaction on a charging station")
	public Uni<RequestStartTransactionResultGraphql> requestStartTransaction(final @Id @Name("id") @NonNull long id,
																		   final @Name("evseId") Integer evseId) {

		// TODO: construct valid IdToken and charging profile
		final IdToken idToken = new IdToken("dummy_id_token", IdTokenType.Central, List.of());
		final long remoteStartId = -1;
		
		return this.chargingStationService.requestStartTransaction(id, evseId, remoteStartId, idToken)
				.onItem()
				.transform(this.entityMapper::createRequestStartTransactionResultGraphql);
	}

	@Mutation("requestStopTransaction")
	@Description("Stop a transaction on a charging station")
	public Uni<RequestStopTransactionResultGraphql> requestStopTransaction(final @Id @Name("id") @NonNull long id,
																		 final @Name("transactionId") @NonNull String transactionId) {

		return this.chargingStationService.requestStopTransaction(id, transactionId)
				.onItem()
				.transform(this.entityMapper::createRequestStopTransactionResultGraphql);
	}

	@Query("diagnostics")
	@Description("Get diagnostics data for a charging station")
	public Uni<DiagnosticsResultGraphql> getDiagnostics(
			final @Id @Name("chargingStationId") @NonNull long chargingStationId,
			final @Name("location") @NonNull String location,
			final @Name("retries") Integer retries,
			final @Name("retryInterval") Integer retryInterval,
			final @Name("startTime") ZonedDateTime startTime,
			final @Name("stopTime") ZonedDateTime stopTime) {

		return this.chargingStationService.getDiagnostics(chargingStationId, location, retries, retryInterval, startTime, stopTime)
				.onItem()
				.transform(this.entityMapper::createDiagnosticsResultGraphql);
	}

	@Mutation("triggerMessage")
	@Description("Trigger a message for a charging station")
	public Uni<TriggerMessageResultGraphql> triggerMessage(final @Id @Name("chargingStationId") @NonNull long chargingStationId,
														   final @Name("messageTrigger") @NonNull MessageTrigger messageTrigger,
														   final @Name("connectorId") Integer connectorId) {

		return this.chargingStationService.triggerMessage(chargingStationId, messageTrigger, connectorId)
				.onItem()
				.transform(this.entityMapper::createTriggerMessageResultGraphql);
	}


	@Description("Get reports of a charging station")
    public Uni<Collection<@NonNull ReportGraphql>> getReports(
    		final @Source ChargingStationGraphql chargingStationGraphql) {

		return VertxUtil.runOnDuplicateContex(() -> this.reportService
        		.getReports(chargingStationGraphql.id())
        		.onItem()
        		.transform((reports) -> reports.stream()
	        		.map(this.entityMapper::createReportGraphql).toList()));
    }

	@Description("Get variables of a charging station")
    public Uni<Collection<@NonNull ChargingStationVariableGraphql>> getChargingStationVariables(
    		final @Source ChargingStationGraphql chargingStationGraphql) {


		return VertxUtil.runOnDuplicateContex(() -> this.managerService
        		.getChargingStationVariables(chargingStationGraphql.id())
        		.onItem()
        		.transform((chargingStationVariables) -> chargingStationVariables.stream()
	        		.map(this.entityMapper::createChargingStationVariableGraphql).toList()));
    }

	@Description("Get EVSE-s of a charging station")
    public Uni<Collection<@NonNull EvseGraphql>> getEvses(
    		final @Source ChargingStationGraphql chargingStationGraphql) {

	    return VertxUtil.runOnDuplicateContex(() -> this.evseResolver.getEvses(chargingStationGraphql.id()));
    }
}